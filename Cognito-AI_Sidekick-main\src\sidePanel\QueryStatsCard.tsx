import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { cn } from "@/src/background/util";
import { useConfig } from './ConfigContext';

export const QueryStatsCard = () => {
  const { config, updateConfig } = useConfig();
  const showQueryStats = config?.showQueryStats ?? true;

  const handleToggle = (checked: boolean) => {
    updateConfig({ showQueryStats: checked });
  };

  return (
    <div className={cn(
      "bg-card border border-border rounded-xl shadow-sm",
      "hover:shadow-md hover:border-border/80",
      "overflow-hidden"
    )}>
      <div className={cn(
        "px-6 py-4 border-b border-border/50"
      )}>
        <div>
          <h3 className="text-apple-title3 font-semibold text-foreground">Query Statistics</h3>
          <p className="text-apple-footnote text-muted-foreground">Control visibility of query processing details</p>
        </div>
      </div>

      <div className="p-6">
        <div className="space-y-4">
          <div className="flex items-start justify-between gap-4">
            <div className="space-y-2 flex-1">
              <Label htmlFor="showQueryStats" className="text-apple-body font-medium text-foreground">
                Show Query Statistics Panel
              </Label>
              <p className="text-apple-caption2 text-muted-foreground leading-relaxed">
                Display a collapsible panel above the chat input showing query processing details including URLs searched, token count, generation speed, and processing time after AI responses complete
              </p>
            </div>
            <div className="flex-shrink-0 pt-1">
              <Switch
                id="showQueryStats"
                checked={showQueryStats}
                onCheckedChange={handleToggle}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
