import { events } from 'fetch-event-stream';
import '../types/config.ts';
import type { Config, Model } from 'src/types/config';

interface ApiMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

const cleanResponse = (response: string): string => {
  return response
    .replace(/<think>[\s\S]*?<\/think>/g, '')
    .replace(/["']/g, '')
    .trim();
};

export const processQueryWithAI = async (
  query: string,
  config: Config,
  currentModel: Model,
  authHeader?: Record<string, string>,
  abortSignal?: AbortSignal,
  contextMessages: ApiMessage[] = [],
  temperatureOverride?: number
): Promise<string> => {
  try {
   if (!currentModel?.host) {
    console.error('processQueryWithAI: currentModel or currentModel.host is undefined. Cannot determine API URL.');
    return query;
  }

  const formattedContext = contextMessages
      .map(msg => `{{${msg.role}}}: ${msg.content}`)
      .join('\n');
  const systemPrompt = `You are a Google search query optimizer. Your task is to rewrite user's input [The user's raw input && chat history:${formattedContext}].
\n
Instructions:
**Important** No Explanation, just the optimized query!
\n
1. Extract the key keywords and named entities from the user's input.
2. Correct any obvious spelling errors.
3. Remove unnecessary words (stop words) unless they are essential for the query's meaning.
4. If the input is nonsensical or not a query, return the original input.
5. Using previous chat history to understand the user's intent.
\n
Output:
'The optimized Google search query'
\n
Example 1:
Input from user ({{user}}): where can i find cheep flights to london
Output:
'cheap flights London'
\n
Example 2:
Context: {{user}}:today is a nice day in paris i want to have a walk and find a restaurant to have a nice meal. {{assistant}}: Bonjour, it's a nice day!
Input from user ({{user}}): please choose me the best restarant
Output:
'best restaurants Paris France'
\n
Example 3:
Input from user ({{user}}): asdf;lkjasdf
Output:
'asdf;lkjasdf'
`;

    const urlMap: Record<string, string> = {
      ollama: `${config?.ollamaUrl || ''}/api/chat`,
    };
    const apiUrl = urlMap[currentModel.host];
    if (!apiUrl) {
      console.error('processQueryWithAI: Could not determine API URL for host:', currentModel.host);
      return query;
    }

    console.log(`processQueryWithAI: Using API URL: ${apiUrl} for host: ${currentModel.host}`);
    console.log('Formatted Context for Prompt:', formattedContext);

    const requestBody: {
      model: string;
      messages: ApiMessage[];
      stream: boolean;
      temperature?: number;
    } = {
      model: config?.selectedModel || currentModel.id || '',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: query }
      ],
      stream: false
    };

    let effectiveTemperature: number | undefined = undefined;
    if (temperatureOverride !== undefined) {
      effectiveTemperature = temperatureOverride;
    } else if (config.temperature !== undefined) {
      effectiveTemperature = config.temperature;
    }

    if (effectiveTemperature !== undefined) {
      requestBody.temperature = effectiveTemperature;
    }

    const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            ...(authHeader || {})
        },
        signal: abortSignal,
        body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
        const errorBody = await response.text();
        console.error(`API request failed with status ${response.status}: ${errorBody}`);
        throw new Error(`API request failed: ${response.statusText}`);
    }

    const responseData = await response.json();
    const rawContent = responseData?.choices?.[0]?.message?.content;
    return typeof rawContent === 'string'
      ? cleanResponse(rawContent)
      : query;

  } catch (error: any) {
    if (abortSignal?.aborted || (error instanceof Error && error.name === 'AbortError')) {
      console.log('processQueryWithAI: Operation aborted.');
      throw error;
    }
    console.error('processQueryWithAI: Error during execution:', error);
    return query;
  }
};

export const urlRewriteRuntime = async function (domain: string) {
  try {
    const url = new URL(domain);
    if (url.protocol === 'chrome:') return;

    const domains = [url.hostname];
    const origin = `${url.protocol}//${url.hostname}`;

    const rules = [
      {
        id: 1,
        priority: 1,
        condition: { requestDomains: domains },
        action: {
          type: 'modifyHeaders',
          requestHeaders: [
            {
              header: 'Origin',
              operation: 'set' as chrome.declarativeNetRequest.HeaderOperation,
              value: origin
            }
          ]
        }
      }
    ];

    await chrome.declarativeNetRequest.updateDynamicRules({
      removeRuleIds: rules.map(r => r.id),
      addRules: rules as chrome.declarativeNetRequest.Rule[]
    });
  } catch (error) {
    console.debug('URL rewrite skipped:', error);
  }
};

const extractMainContent = (htmlString: string): string => {
    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlString, 'text/html');

        doc.querySelectorAll(
            'script, style, nav, footer, header, svg, img, noscript, iframe, form, aside, .sidebar, .ad, .advertisement, .banner, .popup, .modal, .cookie-banner, link[rel="stylesheet"], button, input, select, textarea, [role="navigation"], [role="banner"], [role="contentinfo"], [aria-hidden="true"]'
        ).forEach(el => el.remove());

        let contentElement = doc.querySelector('main')
            || doc.querySelector('article')
            || doc.querySelector('.content')
            || doc.querySelector('#content')
            || doc.querySelector('.main-content')
            || doc.querySelector('#main-content')
            || doc.querySelector('.post-content')
            || doc.body;

        let text = contentElement?.textContent || '';
        text = text.replace(/\s+/g, ' ').trim();
        text = text.split('\n').filter(line => line.trim().length > 20).join('\n');

        return text;
    } catch (error) {
        console.error("Error parsing HTML for content extraction:", error);
        return "[Error extracting content]";
    }
};

interface WikiSearchResultBlock {
    document_title: string;
    section_title: string;
    content: string;
    block_type: "text" | "table" | "infobox";
    language: string;
    url?: string | null;
    last_edit_date?: string | null;
    similarity_score: number;
    probability_score: number;
    summary?: string[];
}

interface WikiQueryResult {
    results: WikiSearchResultBlock[];
}

interface GoogleCustomSearchItem {
  title: string;
  link: string;
  snippet: string;
  htmlTitle?: string;
  htmlSnippet?: string;
  pagemap?: Record<string, any>;
}

interface GoogleCustomSearchResponse {
  kind?: string;
  items?: GoogleCustomSearchItem[];
  error?: {
    code: number;
    message: string;
    errors: Array<{ message: string; domain: string; reason: string; }>;
  };
  searchInformation?: {
    totalResults?: string;
  }
}

export const webSearch = async (
    query: string,
    config: Config,
    abortSignal?: AbortSignal
): Promise<string> => {
    console.log('[webSearch] Received query:', query);
    console.log('[webSearch] Web Mode from config:', config?.webMode);

    const webMode = config.webMode;
    const maxLinksToVisit = config.serpMaxLinksToVisit ?? 3;
    const charLimitPerPage = (config.webLimit && config.webLimit !== 128) ? config.webLimit * 1000 : Infinity;

    const effectiveSignal = abortSignal || new AbortController().signal;

    console.log(`Performing ${webMode} search for: "${query}"`);
    if (webMode === 'Google') {
        console.log(`[webSearch - ${webMode}] Max links to visit for content scraping: ${maxLinksToVisit}`);
    }

    if (!webMode) {
        console.error('[webSearch] Web search mode is undefined. Aborting search. Config was:', JSON.stringify(config));
        return `Error: Web search mode is undefined. Please check your configuration.`;
    }

    try {
        if (webMode === 'Google') {
            const serpTimeoutController = new AbortController();
            const serpApiTimeoutId = setTimeout(() => {
                console.warn(`[webSearch - ${webMode}] SERP API call timed out after 15s.`);
                serpTimeoutController.abort();
            }, 15000);

            const signalForSerpFetch = (typeof AbortSignal.any === 'function')
                ? AbortSignal.any([effectiveSignal, serpTimeoutController.signal])
                : effectiveSignal;

          // Enhanced Google search URL with additional parameters for better results
          const baseUrl = `https://www.google.com/search?q=${encodeURIComponent(query)}&hl=en&gl=us&num=10&start=0&safe=off&filter=0`;

            console.log(`[webSearch - ${webMode}] Fetching SERP from: ${baseUrl}`);

            const response = await fetch(baseUrl, {
                signal: signalForSerpFetch,
                method: 'GET',
                headers: {
                    // Updated User-Agent to match current Chrome version
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Cache-Control': 'max-age=0',
                    'Referer': 'https://www.google.com/',
                }
            }).finally(() => {
                clearTimeout(serpApiTimeoutId);
            });

            console.log(`[webSearch - ${webMode}] Response status: ${response.status} ${response.statusText}`);
            console.log(`[webSearch - ${webMode}] Response headers:`, Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`[webSearch - ${webMode}] Error response body:`, errorText.substring(0, 1000));

                if (response.status === 429) {
                    throw new Error(`Google search rate limited (429). Please try again later.`);
                } else if (response.status === 403) {
                    throw new Error(`Google search access forbidden (403). Google may be blocking automated requests.`);
                } else if (response.status >= 400 && response.status < 500) {
                    throw new Error(`Google search client error (${response.status}): ${response.statusText}`);
                } else {
                    throw new Error(`Google search server error (${response.status}): ${response.statusText}`);
                }
            }

            if (effectiveSignal.aborted) throw new Error("Web search operation aborted.");

            const htmlString = await response.text();
            console.log(`[webSearch - ${webMode}] SERP HTML length: ${htmlString.length} characters`);
            console.log(`[webSearch - ${webMode}] SERP HTML (first 500 chars):`, htmlString.substring(0, 500));

            // Check if we got a CAPTCHA or blocked page
            if (htmlString.includes('captcha') || htmlString.includes('unusual traffic') || htmlString.includes('blocked')) {
                console.warn(`[webSearch - ${webMode}] Detected potential CAPTCHA or blocking page`);
                return `Error: Google search may be blocked. The page contains CAPTCHA or blocking content. Please try again later.`;
            }

            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(htmlString, 'text/html');

            interface SearchResult {
                title: string;
                snippet: string;
                url: string | null;
                content?: string;
            }
            const searchResults: SearchResult[] = [];

            // Google search result parsing - Updated selectors for current Google structure
            const resultSelectors = [
                'div.g',           // Classic Google result container
                'div.MjjYud',      // Modern Google result container
                'div.hlcw0c',      // Alternative container
                'div.kvH3mc',      // Another variant
                'div.tF2Cxc',      // Recent Google structure
                'div.yuRUbf'       // Link container variant
            ];

            // Try multiple selector strategies
            let resultElements: Element[] = [];
            for (const selector of resultSelectors) {
                const elements = Array.from(htmlDoc.querySelectorAll(selector));
                if (elements.length > 0) {
                    resultElements = elements;
                    console.log(`[webSearch - ${webMode}] Found ${elements.length} results using selector: ${selector}`);
                    break;
                }
            }

            // If no results found with standard selectors, try broader search
            if (resultElements.length === 0) {
                resultElements = Array.from(htmlDoc.querySelectorAll('div[data-ved], div[data-hveid]'));
                console.log(`[webSearch - ${webMode}] Fallback: Found ${resultElements.length} results using data attributes`);
            }

            resultElements.forEach((result, index) => {
                try {
                    // Multiple strategies to find the link
                    let linkEl = result.querySelector('a[href^="http"]') ||
                                result.querySelector('a[href^="/url?q="]') ||
                                result.querySelector('a[href]');

                    let url = linkEl?.getAttribute('href');

                    // Handle Google's redirect URLs
                    if (url && url.startsWith('/url?q=')) {
                        const urlParams = new URLSearchParams(url.substring(6));
                        url = urlParams.get('q') || url;
                    }

                    // Multiple strategies to find the title
                    const titleEl = result.querySelector('h3') ||
                                   result.querySelector('h2') ||
                                   result.querySelector('[role="heading"]') ||
                                   result.querySelector('a[href] > div');

                    const title = titleEl?.textContent?.trim() || '';

                    // Multiple strategies to find the snippet
                    let snippet = '';
                    const snippetSelectors = [
                        'div[style*="-webkit-line-clamp"]',
                        'div[data-sncf="1"]',
                        '.VwiC3b span',
                        '.MUxGbd span',
                        '.s3v9rd',
                        '.st',
                        'span[style*="-webkit-line-clamp"]'
                    ];

                    for (const selector of snippetSelectors) {
                        const snippetEls = result.querySelectorAll(selector);
                        if (snippetEls.length > 0) {
                            snippet = Array.from(snippetEls).map(el => el.textContent).join(' ').replace(/\s+/g, ' ').trim();
                            break;
                        }
                    }

                    // Fallback snippet extraction
                    if (!snippet && title) {
                        const containerText = result.textContent || '';
                        const titleIndex = containerText.indexOf(title);
                        if (titleIndex !== -1) {
                            snippet = containerText.substring(titleIndex + title.length).replace(/\s+/g, ' ').trim().substring(0, 300);
                        }
                    }

                    // Validate and add result
                    if (title && url && url.startsWith('http') && !url.includes('google.com/search')) {
                        searchResults.push({ title, snippet, url });
                        console.log(`[webSearch - ${webMode}] Parsed result ${index + 1}: ${title.substring(0, 50)}...`);
                    }
                } catch (error) {
                    console.warn(`[webSearch - ${webMode}] Error parsing result ${index + 1}:`, error);
                }
            });
            console.log(`[webSearch - ${webMode}] Parsed SERP Results (${searchResults.length} found, showing first 5):`, JSON.stringify(searchResults.slice(0, 5)));

            if (searchResults.length === 0) {
                console.warn(`[webSearch - ${webMode}] No search results found on SERP.`);
                console.log(`[webSearch - ${webMode}] HTML document title:`, htmlDoc.title);
                console.log(`[webSearch - ${webMode}] Available div elements:`, Array.from(htmlDoc.querySelectorAll('div')).slice(0, 10).map(div => ({
                    className: div.className,
                    id: div.id,
                    textContent: div.textContent?.substring(0, 100)
                })));

                // Try to detect if we're being blocked
                const bodyText = htmlDoc.body?.textContent?.toLowerCase() || '';
                if (bodyText.includes('captcha') || bodyText.includes('unusual traffic') || bodyText.includes('blocked') || bodyText.includes('robot')) {
                    return 'Error: Google search appears to be blocked. The page contains CAPTCHA or blocking content. Please try again later or check if the extension has proper permissions.';
                }

                return 'No search results found. Google may have changed their page structure or the search was unsuccessful.';
            }

            const linksToFetch = searchResults.slice(0, maxLinksToVisit).filter(r => r.url);
            console.log(`Found ${searchResults.length} results. Attempting to fetch content from top ${linksToFetch.length} links (maxLinksToVisit: ${maxLinksToVisit}).`);

            const pageFetchPromises = linksToFetch.map(async (result) => {
                if (!result.url) return { ...result, content: '[Invalid URL]', status: 'error' };
                if (effectiveSignal.aborted) return { ...result, content: `[Fetching aborted by user: ${result.url}]`, status: 'aborted' };

                console.log(`Fetching content from: ${result.url}`);
                const pageTimeoutController = new AbortController();
                const pageTimeoutId = setTimeout(() => {
                    console.warn(`[webSearch] Page scrape for ${result.url} timed out after 12s.`);
                    pageTimeoutController.abort();
                }, 12000);

                const signalForPageFetch = (typeof AbortSignal.any === 'function')
                    ? AbortSignal.any([effectiveSignal, pageTimeoutController.signal])
                    : effectiveSignal;

                let pageContent = `[Error fetching/processing: Unknown error for ${result.url}]`;
                let pageStatus: 'success' | 'error' | 'aborted' = 'error';
                try {
                    const pageResponse = await fetch(result.url, {
                        signal: signalForPageFetch,
                        method: 'GET',
                        headers: {
                            // Use same User-Agent as Google search for consistency
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                            'Accept-Language': 'en-US,en;q=0.9',
                            'Accept-Encoding': 'gzip, deflate, br',
                            'DNT': '1',
                            'Connection': 'keep-alive',
                            'Upgrade-Insecure-Requests': '1',
                            'Sec-Fetch-Dest': 'document',
                            'Sec-Fetch-Mode': 'navigate',
                            'Sec-Fetch-Site': 'cross-site',
                            'Cache-Control': 'max-age=0',
                        }
                    });
                    if (!pageResponse.ok) throw new Error(`Failed to fetch ${result.url} - Status: ${pageResponse.status}`);
                    const contentType = pageResponse.headers.get('content-type');
                    if (!contentType || !contentType.includes('text/html')) throw new Error(`Skipping non-HTML content (${contentType}) from ${result.url}`);
                    if (effectiveSignal.aborted) throw new Error("Web search operation aborted by user.");

                    const pageHtml = await pageResponse.text();
                    pageContent = extractMainContent(pageHtml);
                    pageStatus = 'success';
                    console.log(`[webSearch - ${webMode}] Successfully fetched and extracted content from: ${result.url} (Extracted Length: ${pageContent.length})`);
                } catch (error: any) {
                    if (error.name === 'AbortError') {
                        if (effectiveSignal.aborted) throw error;
                        pageContent = pageTimeoutController.signal.aborted ? `[Timeout fetching: ${result.url}]` : `[Fetching aborted: ${result.url}]`;
                        pageStatus = 'aborted';
                    } else {
                        pageContent = `[Error fetching/processing: ${error.message}]`;
                        pageStatus = 'error';                    
                    }
                } finally {
                    clearTimeout(pageTimeoutId);
                }
                return { ...result, content: pageContent, status: pageStatus };
            });

            const fetchedPagesResults = await Promise.allSettled(pageFetchPromises);
            if (effectiveSignal.aborted) throw new Error("Web search operation aborted.");

            let combinedResultsText = `Search results for "${query}" using ${webMode}:\n\n`;
            let pageIndex = 0; 
            searchResults.forEach((result, index) => {
                 combinedResultsText += `[Result ${index + 1}: ${result.title}]\n`;
                 combinedResultsText += `URL: ${result.url || '[No URL Found]'}\n`;
                 combinedResultsText += `Snippet: ${result.snippet || '[No Snippet]'}\n`;

                 if (index < linksToFetch.length) {
                     const correspondingFetch = fetchedPagesResults[pageIndex];
                     if (correspondingFetch?.status === 'fulfilled') {
                         const fetchedData = correspondingFetch.value;
                         if (fetchedData.url === result.url) {
                            const contentPreview = fetchedData.content.substring(0, charLimitPerPage); 
                            combinedResultsText += `Content:\n${contentPreview}${fetchedData.content.length > charLimitPerPage ? '...' : ''}\n\n`;
                         } else {
                             combinedResultsText += `Content: [Content fetch mismatch - data for ${fetchedData.url} found, expected ${result.url}]\n\n`;
                         }
                     } else if (correspondingFetch?.status === 'rejected') {
                         combinedResultsText += `Content: [Error fetching: ${correspondingFetch.reason}]\n\n`;
                     } else {
                         combinedResultsText += `Content: [Fetch status unknown]\n\n`;
                     }
                     pageIndex++;
                 } else {
                     combinedResultsText += `Content: [Not fetched due to link limit]\n\n`;
                 }
            });
            return combinedResultsText.trim();

        } else {
            return `Unsupported web search mode: ${webMode}`;
        }
    } catch (error: any) {
        if (error.name === 'AbortError' && effectiveSignal.aborted) {
            console.log('[webSearch] Operation aborted by signal.');
            throw error;
        }
        console.error('Web search overall failed:', error);
        return `Error performing web search: ${error.message}`;
    }
};


export async function fetchDataAsStream(
      url: string,
      data: Record<string, unknown>,
      onMessage: (message: string, done?: boolean, error?: boolean, tokenCount?: number) => void,
      headers: Record<string, string> = {},
      host: string,
      abortSignal?: AbortSignal
    ) {
      let streamFinished = false;

      const finishStream = (message: unknown, isError: boolean = false) => {
        if (!streamFinished) {
          streamFinished = true;
          let finalMessage: string;
          if (typeof message === 'string') {
            finalMessage = message;
          } else if (message && typeof message === 'object' && 'message' in message && typeof (message as any).message === 'string') {
            finalMessage = (message as any).message;
          } else {
            finalMessage = String(message);
          }
          onMessage(finalMessage, true, isError);
        }
      };

      const checkAborted = () => {
        if (abortSignal?.aborted) throw new Error("Streaming operation aborted by user.");
      };

      const cleanUrl = (url: string) => {
        if (url.endsWith('/')) {
          return url.slice(0, -1);
        }
        return url;
      }

      if (url.startsWith('chrome://')) {
        console.log("fetchDataAsStream: Skipping chrome:// URL:", url);
        return;
      }

      if (url.includes('localhost')) {
        await urlRewriteRuntime(cleanUrl(url));
      }

      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', ...headers },
          body: JSON.stringify(data),
          signal: abortSignal
        });

        if (!response.ok) {
          let errorBody = `Network response was not ok (${response.status})`;
          try {
             const text = await response.text();
             errorBody += `: ${text || response.statusText}`;
          } catch (_) {
             errorBody += `: ${response.statusText}`;
          }
          throw new Error(errorBody);
        }

        let str = '';
        let totalTokens = 0;

        if (host === "ollama") {
            if (!response.body) throw new Error('Response body is null for Ollama');
            const reader = response.body.getReader();
            let done, value;
            while (true) {
              checkAborted();
              ({ value, done } = await reader.read());
              if (done) break;
              const chunk = new TextDecoder().decode(value);
              const jsonObjects = chunk.split('\n').filter(line => line.trim() !== '');

              for (const jsonObjStr of jsonObjects) {
                 if (jsonObjStr.trim() === '[DONE]') {
                    if (abortSignal?.aborted) reader.cancel();
                    finishStream(str);
                    return;
                 }
                 try {
                    const parsed = JSON.parse(jsonObjStr);
                    if (parsed.message?.content) {
                      str += parsed.message.content;
                      // Rough token estimation: ~4 characters per token
                      totalTokens = Math.ceil(str.length / 4);
                      if (!streamFinished) onMessage(str, false, false, totalTokens);
                    }
                    if (parsed.done === true && !streamFinished) {
                       if (abortSignal?.aborted) reader.cancel();
                       finishStream(str);
                       return;
                    }
                 } catch (error) {
                    console.debug('Skipping invalid JSON chunk:', jsonObjStr);
                 }
              }
            }
            if (abortSignal?.aborted) reader.cancel();
            finishStream(str);

          } else {
             throw new Error(`Unsupported host specified: ${host}`);
          }

      } catch (error) {
        if (abortSignal?.aborted) {
          console.log(`[fetchDataAsStream] Operation aborted via signal as expected. Details:`, error);
          finishStream("", false);
        } else if (error instanceof Error && error.name === 'AbortError') {
          console.log(`[fetchDataAsStream] AbortError (name check) caught. Operation was cancelled. Details:`, error);
          finishStream("", false);
        } else {
          console.error('Error in fetchDataAsStream (unexpected):', error);
          finishStream(error instanceof Error ? error.message : String(error), true);
        }
      }
    }

/**
 * Scrape the main content from a given URL using the same headers as SERP scraping.
 */
export async function scrapeUrlContent(url: string, abortSignal?: AbortSignal): Promise<string> {
  const controller = new AbortController();
  const signal = abortSignal || controller.signal;
  const timeoutId = !abortSignal ? setTimeout(() => controller.abort(), 12000) : null;
  try {
    const response = await fetch(url, {
      signal: signal,
      method: 'GET',
      headers: {
        // Use consistent User-Agent across all requests
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'cross-site',
        'Cache-Control': 'max-age=0',
      }
    });
    if (timeoutId) clearTimeout(timeoutId);
    if (signal.aborted) throw new Error("Scraping aborted by user.");
    if (!response.ok) throw new Error(`Failed to fetch ${url} - Status: ${response.status}`);
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('text/html')) throw new Error(`Skipping non-HTML content (${contentType}) from ${url}`);
    const html = await response.text();
    return extractMainContent(html);
  } catch (error: any) {
    if (timeoutId) clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
        return `[Scraping URL aborted: ${url}]`;
    }
    return `[Error scraping URL: ${url} - ${error.message}]`;
  }
}

/**
 * Debug function to test Google search functionality
 * Can be called from browser console: testGoogleSearchDebug("test query")
 */
export async function testGoogleSearchDebug(query: string = "test search") {
  console.log('🔍 Testing Google search functionality...');

  const testConfig: Config = {
    webMode: 'Google',
    serpMaxLinksToVisit: 2,
    webLimit: 16,
    personas: {},
    persona: 'Scholar',
    contextLimit: 60,
    temperature: 0.7,
    maxTokens: 32480,
    topP: 0.95,
    presencepenalty: 0,
    models: [],
    chatMode: 'web',
    ollamaUrl: 'http://localhost:11434',
    ollamaConnected: false,
    fontSize: 14,
    panelOpen: false,
    computeLevel: 'low',
    userName: 'user',
    userProfile: '',
  };

  try {
    const result = await webSearch(query, testConfig);
    console.log('✅ Search completed successfully!');
    console.log('📄 Result length:', result.length);
    console.log('📋 Result preview:', result.substring(0, 500) + '...');
    return result;
  } catch (error) {
    console.error('❌ Search failed:', error);
    throw error;
  }
}

// Make the debug function available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).testGoogleSearchDebug = testGoogleSearchDebug;
}