import React from 'react';
import { cn } from "@/src/background/util";

interface WelcomeInterfaceProps {}

interface SuggestedPrompt {
  title: string;
  description: string;
  icon: string;
}

const suggestedPrompts: SuggestedPrompt[] = [
  {
    title: "Monitor industry news",
    description: "Stay updated with the latest developments in your field",
    icon: "💼"
  },
  {
    title: "Creative writing help",
    description: "Get assistance with creative and engaging content",
    icon: "📚"
  },
  {
    title: "Fact-check information",
    description: "Verify claims and analyze information critically",
    icon: "🤔"
  }
];

export const WelcomeInterface: React.FC<WelcomeInterfaceProps> = () => {

  return (
    <div className="flex flex-col items-center justify-center h-full px-6 py-8 min-h-[400px]">
      {/* Welcome Header */}
      <div className="text-center mb-10 max-w-md">
        <h1 className="text-3xl font-semibold text-foreground mb-4 font-['SF_Pro_Display',_system-ui,_sans-serif] tracking-tight">
          Welcome to Chromepanion
        </h1>
        <p className="text-foreground/70 text-base leading-relaxed font-['SF_Pro_Text',_system-ui,_sans-serif]">
          Your private AI web search assistant. Don't forget to choose an AI persona. That Friend guy sounds like a bro!
        </p>
      </div>

      {/* Suggested Prompts Grid */}
      <div className="grid grid-cols-1 gap-4 w-full max-w-lg">
        {suggestedPrompts.map((prompt, index) => (
          <div
            key={index}
            className={cn(
              "relative p-5 text-left rounded-2xl border border-border/20 bg-background/50"
            )}
          >
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-10 h-10 rounded-xl bg-muted/50 flex items-center justify-center text-xl">
                {prompt.icon}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-foreground text-base mb-2 font-['SF_Pro_Text',_system-ui,_sans-serif]">
                  {prompt.title}
                </h3>
                <p className="text-foreground/60 text-sm leading-relaxed font-['SF_Pro_Text',_system-ui,_sans-serif]">
                  {prompt.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer Note */}
      <div className="mt-10 text-center">
        <p className="text-foreground/50 text-sm font-['SF_Pro_Text',_system-ui,_sans-serif]">
          All processing happens locally with your Ollama models
        </p>
      </div>
    </div>
  );
};
