# ChromePanion

A private, local AI-powered web search assistant that connects exclusively to local AI models (Ollama) to analyze web search results through specialized AI personas. Built with modern web technologies and optimized for performance.

## ✨ Features

### 🔒 Privacy-First Design
- **100% Local Processing:** All AI processing happens locally via Ollama
- **No External AI Services:** No data sent to OpenAI, Claude, or other cloud providers
- **Private Search:** Your search queries and results stay on your device

### 🤖 AI-Powered Analysis
- **10 Specialized Personas:** Each with unique analysis styles and expertise
  - 🎓 **Scholar:** Academic, research-focused analysis
  - 💼 **Executive:** Business and strategic insights
  - 📚 **Storyteller:** Narrative and creative perspectives
  - 🤔 **Skeptic:** Critical thinking and fact-checking
  - 🧭 **Mentor:** Guidance and educational approach
  - 🔍 **Investigator:** Deep research and fact-finding
  - ⚙️ **Pragmatist:** Practical, solution-oriented analysis
  - ⚡ **Enthusiast:** Energetic and optimistic viewpoints
  - 🎨 **Curator:** Content organization and curation
  - 😊 **Friend:** Casual, conversational tone

### 🌐 Web Integration
- **Google Search Integration:** Seamlessly fetch and analyze search results
- **PDF Processing:** Extract and analyze text from PDF documents
- **Page Content Analysis:** Analyze current webpage content
- **Smart Context:** Automatically includes relevant page information

### 🎨 Modern Interface
- **Apple Design Language:** Clean, minimalist aesthetic
- **Light/Dark Theme:** Automatic theme switching
- **Responsive Design:** Works seamlessly in Chrome's side panel
- **Chat History:** Track and revisit your conversations
- **Auto-Generated Titles:** Smart chat naming for easy organization
- **Query Statistics:** Optional panel showing processing details (URLs searched, tokens, speed)

## 🚀 Installation

### Prerequisites
- **Node.js 18+** (recommended: Node.js 20+)
- **Ollama** installed and running locally
- **Chrome Browser** (latest version)

### From Source
1. **Clone and setup:**
   ```bash
   git clone https://github.com/user/ChromePanion.git
   cd ChromePanion
   npm install
   ```

2. **Build the extension:**
   ```bash
   # Development build (with watch mode)
   npm start

   # Production build (optimized)
   npm run build

   # Build with bundle analysis
   npm run build:analyze
   ```

3. **Load in Chrome:**
   - Open Chrome → `chrome://extensions`
   - Enable "Developer mode" (top right toggle)
   - Click "Load unpacked"
   - Select the `dist/chrome` folder

### Ollama Setup
1. **Install Ollama:** Visit [ollama.ai](https://ollama.ai) and download for your OS
2. **Start Ollama:** Run `ollama serve` (usually starts automatically)
3. **Pull a model:** `ollama pull llama3.2` (or your preferred model)
4. **Verify:** Check `http://localhost:11434` is accessible

## 📖 Usage

### Getting Started
1. **Open ChromePanion:** Click the extension icon or use Chrome's side panel
2. **Configure Settings:**
   - Set Ollama endpoint (default: `http://localhost:11434`)
   - Select your preferred AI model
   - Choose default persona
3. **Start Searching:** Type your query and let AI analyze the results

### Search Modes
- **Web Search:** Analyze Google search results with AI insights
- **Page Analysis:** Get AI analysis of the current webpage
- **PDF Processing:** Extract and analyze PDF content
- **Chat Mode:** Direct conversation with your chosen AI persona

### Tips for Best Results
- **Be Specific:** Clear, detailed queries get better analysis
- **Choose the Right Persona:** Match the persona to your task
- **Use Context:** ChromePanion automatically includes relevant page context
- **Review History:** Use chat history to track your research

## 🛠️ Development

### Build Commands
```bash
# Development
npm start              # Watch mode with hot reload
npm run clear          # Clean build artifacts

# Production
npm run build          # Optimized production build
npm run build:check    # Build + bundle size analysis
npm run build:analyze  # Build + interactive bundle analyzer

# Quality
npm run lint           # ESLint checking
npm run format         # Prettier formatting
```

### Project Structure
```
src/
├── background/        # Extension background scripts
├── content/          # Content script injection
├── sidePanel/        # Main React application
│   ├── components/   # Reusable UI components
│   ├── hooks/        # Custom React hooks
│   ├── icons/        # Centralized icon imports
│   └── utils/        # Utility functions
├── types/            # TypeScript type definitions
└── state/            # Redux state management
```

### Performance Optimizations
ChromePanion is optimized for performance with:
- **Advanced Code Splitting:** 40+ optimized chunks for better caching
- **Dynamic Loading:** PDF.js and heavy features load only when needed
- **Tree Shaking:** Eliminates unused code from the bundle
- **Optimized Icons:** Centralized imports reduce bundle size
- **Smart Caching:** Efficient browser caching strategy

## 🔧 Configuration

### Ollama Models
Compatible with any Ollama model. Recommended models:
- **llama3.2** (3B/8B) - Fast, efficient for most tasks
- **llama3.1** (8B/70B) - More capable for complex analysis
- **mistral** (7B) - Good balance of speed and capability
- **codellama** (7B/13B) - Excellent for technical content

### Settings Options
- **AI Model Selection:** Choose from available Ollama models
- **Default Persona:** Set your preferred analysis style
- **Theme:** Light/dark mode preference
- **Search Preferences:** Customize search behavior
- **Query Statistics:** Toggle visibility of processing details panel
- **Privacy Settings:** Control data handling

## 📊 Bundle Analysis

Monitor bundle performance:
```bash
npm run build:check    # Quick size analysis
npm run build:analyze  # Interactive bundle explorer
```

Current optimization status:
- ✅ All main chunks under 244 KiB
- ✅ Dynamic PDF.js loading
- ✅ Optimized icon imports
- ✅ Advanced code splitting
- ⚠️ PDF worker (934 KiB) - loads only when needed

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and test thoroughly
4. Run quality checks: `npm run lint && npm run build:check`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

Built with modern web technologies:
- **React 19** - UI framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Radix UI** - Accessible components
- **Webpack 5** - Module bundling
- **Ollama** - Local AI inference
