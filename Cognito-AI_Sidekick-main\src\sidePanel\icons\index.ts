// Centralized icon imports to reduce bundle size
// Only import the specific icons we actually use

// Feather Icons (fi)
export {
  FiX,
  FiTrash2,
  FiShare,
  FiChevronLeft,
  FiSettings,
  FiClock,
  FiMoreHorizontal,
  FiSun,
  FiMoon,
  FiShield,
  FiInfo,
  FiCheck,
  FiPlus,
  FiBarChart2
} from 'react-icons/fi';

// Tabler Icons (tb)
export {
  TbReload,
  TbJson,
  TbWorldSearch,
  TbBrowserPlus,
  TbApi
} from 'react-icons/tb';

// Bootstrap Icons (bi)
export {
  BiBrain
} from 'react-icons/bi';

// Font Awesome 6 (fa6)
export {
  FaGoogle
} from 'react-icons/fa6';

// Ionicons (io)
export {
  IoAdd,
  IoTrashOutline
} from 'react-icons/io5';

// Re-export lucide icons that are already optimized
export * from 'lucide-react';
