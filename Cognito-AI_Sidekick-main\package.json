{"name": "chromepanion", "displayName": "ChromePanion", "version": "1.0.0", "description": "ChromePanion uses AI to interpret your needs, letting you guide, query, and control your Chrome browser through natural interaction.", "main": "index.js", "sideEffects": ["*.css", "*.scss", "*.sass", "src/content/index.css"], "scripts": {"start": "npm run clear && cross-env NODE_ENV=development webpack -w --config config/webpack.config.js", "build": "npm run clear && cross-env NODE_ENV=production webpack --config config/webpack.config.js --mode=production", "build:analyze": "npm run clear && cross-env NODE_ENV=production ANALYZE=true webpack --config config/webpack.config.js --mode=production", "build:check": "npm run build && node scripts/bundle-size-check.js", "clear": "rimraf dist && rimraf node_modules/.cache"}, "repository": {"type": "git", "url": "https://github.com/user/ChromePanion"}, "keywords": ["companion", "AI", "LLM", "chrome", "extension"], "author": "user", "license": "MIT", "bugs": {"url": "https://github.com/user/ChromePanion"}, "homepage": "", "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-portal": "^1.1.9", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@types/react": "19.1.6", "@types/react-dom": "19.1.6", "@types/redux-logger": "^3.0.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "defuddle": "^0.6.4", "dotenv": "^16.5.0", "fetch-event-stream": "^0.1.5", "file-loader": "^6.2.0", "generate-json-from-js-webpack-plugin": "^0.1.1", "html-to-image": "^1.11.13", "html-webpack-plugin": "^5.6.3", "localforage": "^1.10.0", "lucide-react": "^0.513.0", "mini-css-extract-plugin": "^2.9.2", "motion": "^12.16.0", "pdfjs-dist": "^5.3.31", "prop-types": "^15.8.1", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-textarea-autosize": "^8.5.9", "redux-thunk": "^3.1.0", "remark-gfm": "^4.0.1", "remark-supersub": "^1.0.0", "tailwind-merge": "^3.3.0", "tailwind-scrollbar": "^4.0.2", "tailwind-scrollbar-hide": "^4.0.0", "tailwindcss": "^4.1.8", "turndown": "^7.2.0", "turndown-plugin-gfm": "^1.0.2", "tw-animate-css": "^1.3.4", "webext-redux": "^4.0.0"}, "devDependencies": {"@eslint/js": "^9.28.0", "@stylistic/eslint-plugin-ts": "^4.4.1", "@tailwindcss/postcss": "^4.1.8", "@types/chrome": "^0.0.326", "@types/turndown": "^5.0.5", "@typescript-eslint/eslint-plugin": "^8.33.1", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9.28.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.2.0", "postcss": "^8.5.4", "postcss-loader": "^8.1.1", "prettier": "3.5.3", "redux-logger": "^3.0.6", "rimraf": "^6.0.1", "tailwindcss-animate": "^1.0.7", "terser-webpack-plugin": "^5.3.14", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "webpack": "^5.99.9", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1"}}